import { createRouter, createWebHashHistory } from "vue-router";
import Layout from "@/components/layout/Layout.vue";

// 路由配置
const routes = [
  {
    path: "/",
    component: Layout,
    redirect: "/dashboard",
    children: [
      {
        path: "dashboard",
        name: "Dashboard",
        component: () => import("@/views/Dashboard.vue"),
        meta: { title: "首页", requiresAuth: true },
      },
    ],
  },
  {
    path: "/login",
    name: "<PERSON><PERSON>",
    component: () => import("@/views/auth/Login.vue"),
    meta: { title: "登录" },
  },
  {
    path: "/register",
    name: "Register",
    component: () => import("@/views/auth/Register.vue"),
    meta: { title: "注册" },
  },
  {
    path: "/user",
    component: Layout,
    redirect: "/user/profile",
    meta: { requiresAuth: true },
    children: [
      {
        path: "profile",
        name: "UserProfile",
        component: () => import("@/views/user/UserProfile.vue"),
        meta: { title: "个人信息", requiresAuth: true },
      },
      {
        path: "reservations",
        name: "MyReservations",
        component: () => import("@/views/user/MyReservations.vue"),
        meta: { title: "我的预约", requiresAuth: true },
      },
      {
        path: "records",
        name: "OperationRecords",
        component: () => import("@/views/user/OperationRecords.vue"),
        meta: { title: "操作记录", requiresAuth: true },
      },
      {
        path: "credit",
        name: "CreditRecords",
        component: () => import("@/views/user/CreditRecords.vue"),
        meta: { title: "信誉分记录", requiresAuth: true },
      },
    ],
  },
  {
    path: "/seat",
    component: Layout,
    redirect: "/seat/rooms",
    meta: { requiresAuth: true },
    children: [
      {
        path: "rooms",
        name: "RoomList",
        component: () => import("@/views/seat/RoomList.vue"),
        meta: { title: "自习室列表", requiresAuth: true },
      },
      {
        path: "map",
        name: "SeatMap",
        component: () => import("@/views/seat/SeatMap.vue"),
        meta: { title: "座位地图", requiresAuth: true },
      },
      {
        path: "reservation",
        name: "SeatReservation",
        component: () => import("@/views/seat/SeatReservation.vue"),
        meta: { title: "座位预约", requiresAuth: true },
      },
      {
        path: "reservation/:id",
        name: "ReservationDetail",
        component: () => import("@/views/seat/ReservationDetail.vue"),
        meta: { title: "预约详情", requiresAuth: true },
      },
      {
        path: "checkin",
        name: "CheckInOut",
        component: () => import("@/views/seat/CheckInOut.vue"),
        meta: { title: "签到签退", requiresAuth: true },
      },
    ],
  },

  {
    path: "/help",
    component: Layout,
    children: [
      {
        path: "",
        name: "Help",
        component: () => import("@/views/Help.vue"),
        meta: { title: "帮助中心" },
      },
    ],
  },
  {
    path: "/:pathMatch(.*)*",
    name: "NotFound",
    component: () => import("@/views/NotFound.vue"),
    meta: { title: "页面不存在" },
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title
    ? `${to.meta.title} - 基于国密算法的图书馆自习室座位管理系统`
    : "基于国密算法的图书馆自习室座位管理系统";

  next();

  if (to.matched.some((record) => record.meta.requiresAuth)) {
    // 检查用户是否已登录
    const isLoggedIn = localStorage.getItem("token") !== null;

    if (!isLoggedIn) {
      // 未登录，重定向到登录页
      next({
        path: "/login",
        query: { redirect: to.fullPath },
      });
    } else {
      // 已登录，允许访问
      next();
    }
  } else {
    // 不需要登录，允许访问
    next();
  }
});

export default router;
