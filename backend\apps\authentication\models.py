from django.db import models
from django.utils import timezone
from django.contrib.auth.models import AbstractBaseUser, BaseUserManager, PermissionsMixin
from django.core.cache import cache
import binascii
import json
from utils.crypto import SM4Crypto

class UserManager(BaseUserManager):
    """自定义用户管理器"""

    def create_user(self, student_id, password=None, **extra_fields):
        """
        创建普通用户
        """
        if not student_id:
            raise ValueError('用户必须有学号')

        from utils.crypto import SM3Hasher

        # 计算学号的SM3哈希值用于索引
        student_id_hash = SM3Hasher.hash(student_id)

        # 使用系统配置的SM4密钥进行数据加密
        # 为避免循环导入，在此处直接获取密钥
        try:
            from .models import SystemConfig
            sm4_key = SystemConfig.get_sm4_key()
        except:
            # 如果系统配置不可用，使用临时密钥（仅在初始化时）
            sm4_key = SM4Crypto.generate_key()

        # 加密学号
        encrypted_student_id = SM4Crypto.encrypt(sm4_key, student_id)
        student_id_binary = binascii.unhexlify(encrypted_student_id)

        # 创建用户
        user = self.model(
            student_id=student_id_binary,
            student_id_hash=student_id_hash,
            **extra_fields
        )

        # 设置密码
        if password:
            user.set_password(password)

        user.save(using=self._db)
        return user

    def create_superuser(self, student_id, password=None, **extra_fields):
        """
        创建超级用户
        """
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('status', 'active')

        if extra_fields.get('is_staff') is not True:
            raise ValueError('超级用户必须有is_staff=True')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('超级用户必须有is_superuser=True')

        return self.create_user(student_id, password, **extra_fields)


class User(models.Model):
    """用户模型"""
    id = models.BigAutoField(primary_key=True, verbose_name='主键ID')
    student_id = models.BinaryField(max_length=64, verbose_name='SM4加密的学号')
    student_id_hash = models.CharField(max_length=64, unique=True, verbose_name='SM3哈希的学号(用于索引)')
    password = models.CharField(max_length=64, verbose_name='SM3哈希的密码')
    salt = models.CharField(max_length=32, null=True, blank=True, verbose_name='密码盐值')
    iterations = models.IntegerField(default=10000, verbose_name='密码哈希迭代次数')
    public_key = models.TextField(null=True, blank=True, verbose_name='SM2公钥')
    public_key_expires = models.DateTimeField(null=True, blank=True, verbose_name='公钥过期时间')
    email = models.BinaryField(max_length=128, null=True, blank=True, verbose_name='SM4加密的邮箱')
    phone = models.BinaryField(max_length=64, null=True, blank=True, verbose_name='SM4加密的手机号')
    status = models.CharField(max_length=20, default='active', verbose_name='状态(active/disabled/blacklisted/locked)')
    credit_score = models.IntegerField(default=100, verbose_name='信誉分')
    login_attempts = models.IntegerField(default=0, verbose_name='登录失败次数')
    last_login = models.DateTimeField(null=True, blank=True, verbose_name='最后登录时间')
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    # Django admin兼容字段
    is_staff = models.BooleanField(default=False, verbose_name='是否为管理员')
    is_superuser = models.BooleanField(default=False, verbose_name='是否为超级用户')
    is_active = models.BooleanField(default=True, verbose_name='是否激活')

    objects = UserManager()

    USERNAME_FIELD = 'student_id_hash'
    REQUIRED_FIELDS = []

    class Meta:
        db_table = 'user'
        verbose_name = '用户'
        verbose_name_plural = '用户'
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['credit_score']),
        ]

    def __str__(self):
        return f"User {self.id} - {self.student_id_hash[:10]}..."

    def get_username(self):
        """
        返回用户的唯一标识符
        """
        return self.student_id_hash

    def set_password(self, raw_password):
        """
        设置用户密码
        """
        if not raw_password:
            self.password = None
            self.salt = None
            self.iterations = 10000
        else:
            from utils.crypto import SM3Hasher
            password_hash_result = SM3Hasher.hash_with_salt(raw_password)
            self.password = password_hash_result['hash']
            self.salt = password_hash_result['salt']
            self.iterations = password_hash_result['iterations']

    def check_password(self, raw_password):
        """
        检查密码是否正确
        """
        if not self.password or not raw_password:
            return False

        from utils.crypto import SM3Hasher
        return SM3Hasher.verify(raw_password, self.password, self.salt, self.iterations)

    def has_perm(self, perm, obj=None):
        """
        检查用户是否有指定权限
        """
        # 超级用户拥有所有权限
        if self.is_superuser:
            return True
        return False

    def has_module_perms(self, app_label):
        """
        检查用户是否有访问指定应用的权限
        """
        # 超级用户拥有所有权限
        if self.is_superuser:
            return True
        return False

    @property
    def is_authenticated(self):
        """
        始终返回True，表示用户已认证
        """
        return True

    @property
    def is_anonymous(self):
        """
        始终返回False，表示用户不是匿名用户
        """
        return False


class UserSession(models.Model):
    """用户会话表"""
    id = models.BigAutoField(primary_key=True, verbose_name='主键ID')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户ID')
    token = models.CharField(max_length=64, verbose_name='JWT令牌ID')
    ip_address = models.CharField(max_length=45, verbose_name='IP地址')
    user_agent = models.TextField(verbose_name='用户代理')
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    expires_at = models.DateTimeField(verbose_name='过期时间')
    is_active = models.BooleanField(default=True, verbose_name='是否活跃')

    class Meta:
        db_table = 'user_session'
        verbose_name = '用户会话'
        verbose_name_plural = '用户会话'
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['token']),
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        return f"Session {self.id} - User {self.user_id}"


class CreditRecord(models.Model):
    """信誉分记录表"""
    id = models.BigAutoField(primary_key=True, verbose_name='主键ID')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户ID')
    delta = models.IntegerField(verbose_name='变动分值')
    reason = models.CharField(max_length=255, verbose_name='变动原因')
    score_after = models.IntegerField(verbose_name='变动后分数')
    operator_id = models.BigIntegerField(null=True, blank=True, verbose_name='操作员ID（管理员操作）')
    operator_type = models.CharField(max_length=10, default='system', verbose_name='操作员类型(system/admin)')
    related_entity = models.CharField(max_length=20, null=True, blank=True, verbose_name='相关实体(reservation/check_in)')
    related_id = models.BigIntegerField(null=True, blank=True, verbose_name='相关实体ID')
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')

    class Meta:
        db_table = 'credit_record'
        verbose_name = '信誉分记录'
        verbose_name_plural = '信誉分记录'
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"Credit Record {self.id} - User {self.user_id} ({self.delta})"


class SystemConfig(models.Model):
    """系统配置模型"""

    CONFIG_TYPES = [
        ('encryption', '加密配置'),
        ('email', '邮件配置'),
        ('security', '安全配置'),
        ('system', '系统配置'),
    ]

    key = models.CharField(max_length=100, unique=True, verbose_name='配置键')
    value = models.TextField(verbose_name='配置值')
    config_type = models.CharField(max_length=20, choices=CONFIG_TYPES, verbose_name='配置类型')
    description = models.TextField(blank=True, verbose_name='配置描述')
    is_encrypted = models.BooleanField(default=False, verbose_name='是否加密存储')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'auth_system_config'
        verbose_name = '系统配置'
        verbose_name_plural = '系统配置'
        ordering = ['config_type', 'key']

    def __str__(self):
        return f"{self.get_config_type_display()}: {self.key}"

    @classmethod
    def get_config(cls, key, default=None):
        """获取配置值"""
        cache_key = f"system_config:{key}"
        value = cache.get(cache_key)

        if value is None:
            try:
                config = cls.objects.get(key=key, is_active=True)
                value = config.get_value()
                # 缓存配置值，有效期1小时
                cache.set(cache_key, value, 3600)
            except cls.DoesNotExist:
                value = default

        return value

    @classmethod
    def set_config(cls, key, value, config_type='system', description='', is_encrypted=False):
        """设置配置值"""
        config, created = cls.objects.get_or_create(
            key=key,
            defaults={
                'config_type': config_type,
                'description': description,
                'is_encrypted': is_encrypted,
            }
        )

        config.set_value(value)
        config.save()

        # 清除缓存
        cache_key = f"system_config:{key}"
        cache.delete(cache_key)

        return config

    def get_value(self):
        """获取配置值（自动解密）"""
        if self.is_encrypted:
            # 使用系统主密钥解密
            master_key = self._get_master_key()
            try:
                return SM4Crypto.decrypt(master_key, self.value)
            except Exception:
                return None
        else:
            # 尝试解析JSON
            try:
                return json.loads(self.value)
            except (json.JSONDecodeError, TypeError):
                return self.value

    def set_value(self, value):
        """设置配置值（自动加密）"""
        if self.is_encrypted:
            # 使用系统主密钥加密
            master_key = self._get_master_key()
            if isinstance(value, (dict, list)):
                value = json.dumps(value)
            self.value = SM4Crypto.encrypt(master_key, str(value))
        else:
            if isinstance(value, (dict, list)):
                self.value = json.dumps(value)
            else:
                self.value = str(value)

    def _get_master_key(self):
        """获取系统主密钥"""
        # 从环境变量或配置文件获取主密钥
        # 这里使用固定密钥，生产环境应该从安全的地方获取
        from django.conf import settings
        return getattr(settings, 'SYSTEM_MASTER_KEY', 'default_master_key_32_characters')

    @classmethod
    def get_sm4_key(cls):
        """获取系统SM4密钥"""
        key = cls.get_config('sm4_encryption_key')
        if not key:
            # 生成新的SM4密钥
            new_key = SM4Crypto.generate_key()
            cls.set_config(
                'sm4_encryption_key',
                new_key,
                config_type='encryption',
                description='系统SM4加密密钥',
                is_encrypted=True
            )
            return new_key
        return key


class PasswordResetToken(models.Model):
    """密码重置令牌模型"""

    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    token = models.CharField(max_length=64, unique=True, verbose_name='重置令牌')
    expires_at = models.DateTimeField(verbose_name='过期时间')
    is_used = models.BooleanField(default=False, verbose_name='是否已使用')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    used_at = models.DateTimeField(null=True, blank=True, verbose_name='使用时间')
    ip_address = models.GenericIPAddressField(null=True, blank=True, verbose_name='IP地址')

    class Meta:
        db_table = 'auth_password_reset_token'
        verbose_name = '密码重置令牌'
        verbose_name_plural = '密码重置令牌'
        ordering = ['-created_at']

    def __str__(self):
        return f"重置令牌 - {self.user.student_id_hash[:8]}..."

    def is_valid(self):
        """检查令牌是否有效"""
        return not self.is_used and self.expires_at > timezone.now()

    def mark_as_used(self):
        """标记令牌为已使用"""
        self.is_used = True
        self.used_at = timezone.now()
        self.save(update_fields=['is_used', 'used_at'])
