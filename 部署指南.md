# 基于国密算法的图书馆自习室座位管理系统 - 部署指南

## 系统要求

### 服务器要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Windows Server 2019+
- **内存**: 最低 4GB，推荐 8GB+
- **存储**: 最低 50GB，推荐 100GB+
- **CPU**: 最低 2核，推荐 4核+

### 软件依赖
- **Python**: 3.8+
- **Node.js**: 16+
- **MySQL**: 8.0+
- **Nginx**: 1.18+ (可选)

## 环境准备

### 1. 安装Python环境
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3 python3-pip python3-venv

# CentOS/RHEL
sudo yum install python3 python3-pip
```

### 2. 安装Node.js
```bash
# 使用NodeSource仓库
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 或使用nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18
```

### 3. 安装MySQL
```bash
# Ubuntu/Debian
sudo apt install mysql-server mysql-client

# 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation
```

## 后端部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd library_seat_system3
```

### 2. 创建虚拟环境
```bash
cd backend
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows
```

### 3. 安装依赖
```bash
pip install -r requirements.txt
```

### 4. 配置环境变量
创建 `.env` 文件：
```bash
# 基本配置
SECRET_KEY=your-very-long-and-random-secret-key-here
DEBUG=False
ALLOWED_HOSTS=your-domain.com,localhost,127.0.0.1

# 数据库配置
DB_NAME=seat_management
DB_USER=seat_user
DB_PASSWORD=your-secure-password
DB_HOST=localhost
DB_PORT=3306

# 邮件配置
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>

# 系统配置
SYSTEM_MASTER_KEY=your-32-character-master-key-here
FRONTEND_URL=https://yourdomain.com
```

### 5. 数据库配置
```bash
# 登录MySQL
mysql -u root -p

# 创建数据库和用户
CREATE DATABASE seat_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'seat_user'@'localhost' IDENTIFIED BY 'your-secure-password';
GRANT ALL PRIVILEGES ON seat_management.* TO 'seat_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 6. 数据库迁移
```bash
# 生成迁移文件
python manage.py makemigrations

# 执行迁移
python manage.py migrate

# 初始化系统配置
python manage.py init_system_config

# 创建超级用户
python manage.py createsuperuser
```

### 7. 收集静态文件
```bash
python manage.py collectstatic --noinput
```

### 8. 测试运行
```bash
python manage.py runserver 0.0.0.0:8000
```

## 前端部署

### 1. 安装依赖
```bash
cd frontend
npm install
```

### 2. 配置环境变量
创建 `.env.production` 文件：
```bash
VUE_APP_API_URL=https://api.yourdomain.com
VUE_APP_WS_URL=wss://api.yourdomain.com/ws
```

### 3. 构建生产版本
```bash
npm run build
```

### 4. 部署静态文件
```bash
# 将dist目录内容复制到Web服务器
cp -r dist/* /var/www/html/
```

## 生产环境配置

### 1. 使用Gunicorn运行Django
```bash
# 安装Gunicorn
pip install gunicorn

# 创建Gunicorn配置文件
cat > gunicorn.conf.py << EOF
bind = "127.0.0.1:8000"
workers = 4
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
EOF

# 启动Gunicorn
gunicorn --config gunicorn.conf.py backend.wsgi:application
```

### 2. 配置Nginx
创建 `/etc/nginx/sites-available/seat_management` 文件：
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    # SSL配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # 前端静态文件
    location / {
        root /var/www/html;
        try_files $uri $uri/ /index.html;
    }
    
    # API请求代理到Django
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # WebSocket代理
    location /ws/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态文件
    location /static/ {
        alias /path/to/your/backend/staticfiles/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 媒体文件
    location /media/ {
        alias /path/to/your/backend/media/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

启用站点：
```bash
sudo ln -s /etc/nginx/sites-available/seat_management /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 3. 配置系统服务
创建 `/etc/systemd/system/seat_management.service` 文件：
```ini
[Unit]
Description=Seat Management Django Application
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/path/to/your/backend
Environment=PATH=/path/to/your/backend/venv/bin
EnvironmentFile=/path/to/your/backend/.env
ExecStart=/path/to/your/backend/venv/bin/gunicorn --config gunicorn.conf.py backend.wsgi:application
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

启用服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable seat_management
sudo systemctl start seat_management
sudo systemctl status seat_management
```

## 数据初始化

### 1. 创建自习室和座位数据
```bash
# 运行数据初始化脚本
python manage.py shell

# 在Python shell中执行
from apps.seat.models import Room, Seat

# 创建自习室（根据实际情况调整）
for floor in range(1, 7):  # 1-6层
    for area in ['东区', '西区']:
        room = Room.objects.create(
            name=f'{floor}层{area}自习室',
            location=f'图书馆{floor}层{area}',
            floor=floor,
            capacity=36,
            open_time='08:00:00',
            close_time='22:00:00',
            status='open',
            description=f'图书馆{floor}层{area}自习室，提供安静的学习环境'
        )
        
        # 为每个自习室创建6x6座位
        for row in range(1, 7):
            for col in range(1, 7):
                seat_number = f'{row}-{col}'
                is_edge_seat = row == 1 or row == 6 or col == 1 or col == 6
                is_power_outlet = is_edge_seat and ((row + col) % 2 == 0)
                is_window_seat = row == 1 or row == 6
                
                Seat.objects.create(
                    room=room,
                    seat_number=seat_number,
                    row=row,
                    column=col,
                    status='available',
                    is_power_outlet=is_power_outlet,
                    is_window_seat=is_window_seat
                )

print("数据初始化完成！")
```

## 监控和维护

### 1. 日志配置
确保日志目录存在并有适当权限：
```bash
sudo mkdir -p /var/log/seat_management
sudo chown www-data:www-data /var/log/seat_management
```

### 2. 定期备份
创建备份脚本 `/usr/local/bin/backup_seat_management.sh`：
```bash
#!/bin/bash
BACKUP_DIR="/backup/seat_management"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u seat_user -p seat_management > $BACKUP_DIR/db_backup_$DATE.sql

# 备份媒体文件
tar -czf $BACKUP_DIR/media_backup_$DATE.tar.gz /path/to/your/backend/media/

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

添加到crontab：
```bash
# 每天凌晨2点备份
0 2 * * * /usr/local/bin/backup_seat_management.sh
```

### 3. 系统监控
可以使用以下工具进行监控：
- **系统监控**: htop, iotop, netstat
- **应用监控**: Django Debug Toolbar (开发环境)
- **日志监控**: tail, grep, logrotate
- **性能监控**: New Relic, Sentry (可选)

## 故障排除

### 常见问题
1. **数据库连接失败**: 检查数据库配置和网络连接
2. **静态文件404**: 检查STATIC_ROOT配置和collectstatic命令
3. **WebSocket连接失败**: 检查Nginx配置和防火墙设置
4. **邮件发送失败**: 检查SMTP配置和网络连接

### 日志查看
```bash
# Django应用日志
tail -f /var/log/seat_management/django.log

# Nginx日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# 系统服务日志
journalctl -u seat_management -f
```

## 安全建议

1. **定期更新**: 保持系统和依赖包的最新版本
2. **防火墙配置**: 只开放必要的端口
3. **SSL证书**: 使用HTTPS加密传输
4. **密码策略**: 使用强密码和定期更换
5. **访问控制**: 限制管理员账户数量
6. **备份验证**: 定期测试备份恢复流程

通过以上步骤，您可以成功部署基于国密算法的图书馆自习室座位管理系统到生产环境。
